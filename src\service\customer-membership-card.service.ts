import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomerMembershipCard } from '../entity/customer-membership-card.entity';
import { BaseService } from '../common/BaseService';
import { MembershipCardType, Customer } from '../entity';
import { CombinedChangeLogService } from './combined-change-log.service';
import { MembershipCardUsageRecordService } from './membership-card-usage-record.service';
import { Op } from 'sequelize';
import { CustomerService } from './customer.service';
import { Service } from '../entity/service.entity';
import { ApplicableScope } from '../common/Constant';
import { CombinedChangeType } from '../entity/combined-change-log.entity';

@Provide()
export class CustomerMembershipCardService extends BaseService<CustomerMembershipCard> {
  @Inject()
  ctx: Context;

  @Inject()
  combinedChangeLogService: CombinedChangeLogService;

  @Inject()
  customerService: CustomerService;

  @Inject()
  membershipCardUsageRecordService: MembershipCardUsageRecordService;

  constructor() {
    super('用户权益卡');
  }

  getModel() {
    return CustomerMembershipCard;
  }

  /** 根据权益卡类型ID获取用户列表 */
  async getUsersByCardType(opt: any) {
    // 确保使用 cardTypeId 进行过滤
    const query = {
      ...opt.query,
      cardTypeId: opt.query.cardTypeId, // 权益卡类型ID已经在控制器中添加到query中
    };

    // 构建Customer查询条件
    const customerWhere: any = {};
    if (opt.query.customer) {
      let customerFilter = opt.query.customer;

      // 如果customer是字符串，尝试解析为JSON
      if (typeof customerFilter === 'string') {
        try {
          customerFilter = JSON.parse(customerFilter);
        } catch (error) {
          console.warn('customer参数解析失败:', error);
          customerFilter = {};
        }
      }

      const { nickname, phone } = customerFilter;
      if (nickname) {
        customerWhere.nickname = { [Op.like]: `%${nickname}%` };
      }
      if (phone) {
        customerWhere.phone = { [Op.like]: `%${phone}%` };
      }
      delete query.customer;
    }

    // 构建include选项
    const includeOptions = [
      {
        model: Customer,
        as: 'customer',
        where:
          Object.keys(customerWhere).length > 0 ? customerWhere : undefined,
        required: Object.keys(customerWhere).length > 0, // 如果有customer查询条件，则必须匹配
      },
    ];

    const result = await this.findAll({
      query,
      offset: opt.offset || 0,
      limit: opt.limit || 20,
      include: includeOptions,
    });
    return result;
  }

  /** 根据用户ID获取权益卡列表 */
  async getCardsByUser(opt: any) {
    // 确保使用 customerId 进行过滤
    const query = {
      ...opt.query,
      customerId: opt.query.customerId, // 用户ID已经在控制器中添加到query中
    };

    const result = await this.findAll({
      query,
      offset: opt.offset || 0,
      limit: opt.limit || 20,
      include: [MembershipCardType],
    });
    return result;
  }

  async findByCustomerId(customerId: number) {
    return await this.findAll({
      query: { customerId },
      include: ['cardType'],
    });
  }

  async findValidCards(customerId: number) {
    const now = new Date();
    return await this.findAll({
      query: {
        customerId,
        status: 'active',
        [Op.or]: [
          { expiryTime: { [Op.gt]: now } }, // 未过期的卡
          { expiryTime: null }, // 无到期时间的卡
        ],
      },
      include: ['cardType'],
    });
  }

  /**
   * 获取用户可用的权益卡列表
   * @param customerId 用户ID
   * @param serviceId 服务ID（可选）
   * @param amount 订单金额（可选）
   * @returns 可用的权益卡列表，包含折扣信息
   */
  async getAvailableMembershipCards(
    customerId: number,
    serviceId?: number,
    amount?: number
  ) {
    // 获取用户有效的权益卡
    const result = await this.findValidCards(customerId);

    // 如果没有传入服务ID，则直接返回所有有效的权益卡
    if (!serviceId) {
      return {
        list: result.list,
        count: result.list.length,
      };
    }

    // 获取服务信息，用于判断权益卡适用范围
    const service = await Service.findByPk(serviceId, {
      include: ['serviceType'],
    });
    if (!service) {
      this.ctx.logger.error('【服务不存在】：', {
        customerId,
        serviceId,
      });
      throw new Error('服务不存在');
    }

    // 检查服务是否支持权益卡抵扣
    if (!service.cardDiscountFlag) {
      this.ctx.logger.warn('【服务不支持权益卡抵扣】：', {
        customerId,
        serviceId,
        serviceName: service.serviceName,
        cardDiscountFlag: service.cardDiscountFlag,
      });
      // 如果服务不支持权益卡抵扣，则返回空列表
      return {
        list: [],
        count: 0,
      };
    }

    // 筛选可用的权益卡并计算折扣信息
    const availableCards = [];

    for (const userCard of result.list) {
      const cardType = userCard.cardType;

      // 检查适用范围
      let isApplicable = false;

      switch (cardType.applicableScope) {
        case ApplicableScope.不限:
        case ApplicableScope.所有服务:
          isApplicable = true;
          break;

        case ApplicableScope.指定服务类别:
          // 检查服务类型是否在适用范围内
          if (service.serviceType && cardType.applicableServiceTypes) {
            isApplicable = cardType.applicableServiceTypes.includes(
              service.serviceType.type
            );
          }
          break;

        case ApplicableScope.指定服务品牌:
          // 检查服务品牌是否在适用范围内
          if (service.serviceType && cardType.applicableServiceCategories) {
            isApplicable = cardType.applicableServiceCategories.includes(
              service.serviceType.id
            );
          }
          break;

        case ApplicableScope.指定服务:
          // 检查服务是否在适用范围内
          if (cardType.applicableServices) {
            isApplicable = cardType.applicableServices.includes(service.id);
          }
          break;

        default:
          // 其他范围（商品相关）不适用于服务
          isApplicable = false;
      }

      if (isApplicable) {
        // 根据卡类型计算折扣信息
        let discountInfo = {};

        if (cardType.type === 'discount' && amount !== undefined) {
          // 折扣卡：计算折扣后金额
          const discountRate = cardType.discountRate || 1;
          const discountedAmount =
            Math.round(amount * discountRate * 100) / 100;
          const discount = Math.round((amount - discountedAmount) * 100) / 100;

          discountInfo = {
            discountType: 'discount',
            discountRate,
            originalAmount: amount,
            discountedAmount,
            discount,
          };
        } else if (cardType.type === 'times') {
          // 次卡：显示剩余次数
          discountInfo = {
            discountType: 'times',
            remainTimes: userCard.remainTimes,
            totalTimes: cardType.usageLimit,
          };
        }

        // 添加到可用权益卡列表
        availableCards.push({
          ...userCard.toJSON(),
          ...discountInfo,
        });
      } else {
        this.ctx.logger.info('【权益卡不适用，跳过】：', {
          cardId: userCard.id,
          cardTypeName: cardType.name,
          applicableScope: cardType.applicableScope,
        });
      }
    }

    // 按照卡类型排序：折扣卡在前（按折扣金额降序），次卡在后
    availableCards.sort((a, b) => {
      // 首先按卡类型排序
      if (a.cardType.type !== b.cardType.type) {
        return a.cardType.type === 'discount' ? -1 : 1;
      }

      // 如果都是折扣卡，按折扣金额降序
      if (a.cardType.type === 'discount') {
        return (b.discount || 0) - (a.discount || 0);
      }

      // 如果都是次卡，按剩余次数降序
      return (b.remainTimes || 0) - (a.remainTimes || 0);
    });

    const finalResult = {
      list: availableCards,
      count: availableCards.length,
    };

    return finalResult;
  }

  async useCard(id: number, orderId: number, isAdditionalService = false) {
    const card = await this.findOne({ where: { id } });
    if (!card) {
      throw new Error('权益卡不存在');
    }

    // 是否已过期
    if (card.expiryTime && card.expiryTime < new Date()) {
      await card.update({ status: 'expired' });
      throw new Error('权益卡已过期');
    }

    // 是否是不限制次数的卡
    const isUnlimited = card.remainTimes === null || card.remainTimes === -1;
    if (!isUnlimited && card.remainTimes <= 0) {
      await card.update({ status: 'used', remainTimes: 0 });
      throw new Error('权益卡次数已用完');
    }

    const beforeRemainTimes = isUnlimited ? '不限' : card.remainTimes;
    const afterRemainTimes = isUnlimited
      ? '不限'
      : (beforeRemainTimes as number) - 1;
    const beforeStatus = card.status;
    let afterStatus = beforeStatus;

    const updateData: any = {
      remainTimes: isUnlimited ? -1 : afterRemainTimes,
    };

    // 如果使用后次数为0，更新状态为已用完
    if (afterRemainTimes === 0) {
      updateData.status = 'used';
      afterStatus = 'used';
    }

    // 更新卡片
    const updatedCard = await card.update({
      ...updateData,
      status: afterStatus,
    });

    // 创建使用记录（只有主订单才创建，追加服务订单因为外键约束问题暂不创建）
    if (!isAdditionalService) {
      await this.membershipCardUsageRecordService.createUsageRecord(
        id,
        orderId
      );

      // 记录变更日志
      await this.combinedChangeLogService.createLog({
        /** 变更类型 */
        changeType: CombinedChangeType.MEMBERSHIP_USE,
        /** 关联的用户权益卡ID */
        cardId: id,
        /** 关联的权益卡类型Id */
        cardTypeId: card.cardTypeId,
        /** 关联的用户ID */
        customerId: card.customerId,
        /** 关联的订单ID */
        orderId,
        /** 变更前状态 */
        beforeStatus,
        /** 变更后状态 */
        afterStatus,
        /** 变更前剩余次数 */
        beforeRemainTimes: String(beforeRemainTimes),
        /** 变更后剩余次数 */
        afterRemainTimes: String(afterRemainTimes),
      });
    }

    // 如果卡片状态变为已用完，检查用户是否还有其他有效的权益卡
    if (afterStatus === 'used') {
      const customerId = card.customerId;
      const hasActiveCard = await this.customerService.hasActiveCard(
        customerId
      );

      // 如果没有其他有效的权益卡，将用户会员状态更新为普通会员
      if (!hasActiveCard) {
        await this.customerService.updateMemberStatus(customerId, 0);
      }
    }

    return updatedCard;
  }

  async addTimes(id: number, times: number, operatorId?: number) {
    const card = await this.findById(id);
    if (!card) {
      throw new Error('权益卡不存在');
    }

    if (card.remainTimes === null || card.remainTimes === -1) {
      // 不限次数的情况，直接返回原权益卡
      return card;
    }

    const beforeRemainTimes = card.remainTimes;
    const afterRemainTimes = beforeRemainTimes + times;
    const beforeStatus = card.status;
    let afterStatus = beforeStatus;

    const updateData: any = {
      remainTimes: afterRemainTimes,
    };

    // 如果卡片状态为已用完，但添加次数后不为0，则更新状态为有效
    if (card.status === 'used' && afterRemainTimes > 0) {
      updateData.status = 'active';
      afterStatus = 'active';
    }

    // 如果卡片已过期，但仍在添加次数，不改变其过期状态
    if (card.status === 'expired') {
      afterStatus = 'expired';
    }

    // 更新卡片
    const updatedCard = await card.update({
      ...updateData,
      status: afterStatus,
    });

    // 记录变更日志
    await this.combinedChangeLogService.createLog({
      changeType: CombinedChangeType.MEMBERSHIP_ADD_TIMES,
      cardId: card.id,
      cardTypeId: card.cardTypeId,
      customerId: card.customerId,
      operatorId: operatorId,
      beforeStatus: beforeStatus,
      afterStatus: afterStatus,
      beforeRemainTimes: String(beforeRemainTimes),
      afterRemainTimes: String(afterRemainTimes),
    });

    // 如果卡片状态从已用完变为有效，更新用户会员状态为权益会员
    if (beforeStatus === 'used' && afterStatus === 'active') {
      const customerId = card.customerId;
      await this.customerService.updateMemberStatus(customerId, 1);
    }

    return updatedCard;
  }

  /**
   * 检查并更新过期的会员卡状态
   * 可以定时调用此方法，将过期的卡片状态更新为expired
   */
  async checkAndUpdateExpiredCards() {
    const now = new Date();
    const result = await this.findAll({
      query: {
        expiryTime: { [Op.lt]: now, [Op.ne]: null }, // 排除无到期时间的卡
        status: 'active',
      },
    });

    const expiredCards = result.list;
    if (expiredCards && expiredCards.length > 0) {
      // 批量更新过期卡片状态并记录日志
      await Promise.all(
        expiredCards.map(async (card: CustomerMembershipCard) => {
          const updatedCard = await card.update({ status: 'expired' });

          // 记录变更日志
          await this.combinedChangeLogService.createLog({
            changeType: CombinedChangeType.MEMBERSHIP_EXPIRE,
            cardId: updatedCard.id,
            cardTypeId: updatedCard.cardTypeId,
            customerId: updatedCard.customerId,
            beforeStatus: 'active',
            afterStatus: 'expired',
            beforeRemainTimes: String(card.remainTimes),
            afterRemainTimes: String(updatedCard.remainTimes),
          });

          // 检查用户是否还有其他有效的权益卡
          const customerId = card.customerId;
          const hasActiveCard = await this.customerService.hasActiveCard(
            customerId
          );

          // 如果没有其他有效的权益卡，将用户会员状态更新为普通会员
          if (!hasActiveCard) {
            await this.customerService.updateMemberStatus(customerId, 0);
          }

          return updatedCard;
        })
      );
    }

    return expiredCards?.length || 0;
  }

  /**
   * 创建用户权益卡
   * @param data 权益卡数据
   * @param isAdminGrant 是否是后台发放
   * @param operatorId 操作人ID（后台发放时需要）
   */
  async createCard(data: any, isAdminGrant = false, operatorId?: number) {
    // 如果没有传入购买时间，使用当前时间作为默认值
    if (!data.purchaseTime) {
      data.purchaseTime = new Date();
    }

    // 创建权益卡
    const card = await this.create(data);

    // 记录变更日志
    if (isAdminGrant && operatorId) {
      // 后台发放
      await this.combinedChangeLogService.createLog({
        changeType: CombinedChangeType.MEMBERSHIP_ADMIN_GRANT,
        cardId: card.id,
        cardTypeId: card.cardTypeId,
        customerId: card.customerId,
        operatorId: operatorId,
        beforeStatus: card.status,
        afterStatus: card.status,
        beforeRemainTimes: String(card.remainTimes),
        afterRemainTimes: String(card.remainTimes),
        description: '后台发放权益卡',
      });
    } else {
      // 用户购买
      await this.combinedChangeLogService.createLog({
        changeType: CombinedChangeType.MEMBERSHIP_PURCHASE,
        cardId: card.id,
        cardTypeId: card.cardTypeId,
        customerId: card.customerId,
        beforeStatus: card.status,
        afterStatus: card.status,
        beforeRemainTimes: String(card.remainTimes),
        afterRemainTimes: String(card.remainTimes),
        description: '用户购买权益卡',
      });
    }

    // 如果卡片状态为有效，更新用户会员状态为权益会员
    if (card.status === 'active') {
      await this.customerService.updateMemberStatus(card.customerId, 1);
    }

    return card;
  }

  /**
   * 后台禁用权益卡
   * @param id 权益卡ID
   * @param operatorId 操作人ID
   * @param description 描述
   */
  async disableCard(id: number, operatorId: number, description?: string) {
    const card = await this.findById(id);
    if (!card) {
      throw new Error('权益卡不存在');
    }

    // 更新卡片状态为过期
    const updatedCard = await card.update({ status: 'expired' });

    // 记录变更日志
    await this.combinedChangeLogService.createLog({
      changeType: CombinedChangeType.MEMBERSHIP_DISABLE,
      cardId: updatedCard.id,
      cardTypeId: updatedCard.cardTypeId,
      customerId: updatedCard.customerId,
      operatorId: operatorId,
      beforeStatus: card.status,
      afterStatus: updatedCard.status,
      beforeRemainTimes: String(card.remainTimes),
      afterRemainTimes: String(updatedCard.remainTimes),
      description: description || '后台禁用权益卡',
    });

    // 检查用户是否还有其他有效的权益卡
    const customerId = card.customerId;
    const hasActiveCard = await this.customerService.hasActiveCard(customerId);

    // 如果没有其他有效的权益卡，将用户会员状态更新为普通会员
    if (!hasActiveCard) {
      await this.customerService.updateMemberStatus(customerId, 0);
    }

    return updatedCard;
  }

  /**
   * 后台撤销权益卡
   * @param id 权益卡ID
   * @param operatorId 操作人ID
   * @param description 描述
   */
  async revokeCard(id: number, operatorId: number, description?: string) {
    const card = await this.findById(id);
    if (!card) {
      throw new Error('权益卡不存在');
    }

    // 更新卡片状态为过期，剩余次数为0
    const updatedCard = await card.update({
      status: 'expired',
      remainTimes: 0,
    });

    // 记录变更日志
    await this.combinedChangeLogService.createLog({
      changeType: CombinedChangeType.MEMBERSHIP_REVOKE,
      cardId: updatedCard.id,
      cardTypeId: updatedCard.cardTypeId,
      customerId: updatedCard.customerId,
      operatorId: operatorId,
      beforeStatus: card.status,
      afterStatus: updatedCard.status,
      beforeRemainTimes: String(card.remainTimes),
      afterRemainTimes: String(updatedCard.remainTimes),
      description: description || '后台撤销权益卡',
    });

    // 检查用户是否还有其他有效的权益卡
    const customerId = card.customerId;
    const hasActiveCard = await this.customerService.hasActiveCard(customerId);

    // 如果没有其他有效的权益卡，将用户会员状态更新为普通会员
    if (!hasActiveCard) {
      await this.customerService.updateMemberStatus(customerId, 0);
    }

    return updatedCard;
  }

  /**
   * 按订单禁用权益卡
   * @param customerId 用户ID
   * @param cardTypeId 权益卡类型ID
   * @param operatorId 操作人ID
   * @param description 描述
   */
  async disableCardsByOrder(
    customerId: number,
    cardTypeId: number,
    operatorId: number,
    description?: string
  ) {
    // 查找该用户该类型的有效权益卡
    const cards = await this.findAll({
      query: {
        customerId,
        cardTypeId,
        status: 'active',
      },
    });

    const disabledCards = [];
    for (const card of cards.list) {
      const updatedCard = await card.update({ status: 'expired' });

      // 记录变更日志
      await this.combinedChangeLogService.createLog({
        changeType: CombinedChangeType.MEMBERSHIP_DISABLE,
        cardId: updatedCard.id,
        cardTypeId: updatedCard.cardTypeId,
        customerId: updatedCard.customerId,
        operatorId: operatorId,
        beforeStatus: 'active',
        afterStatus: updatedCard.status,
        beforeRemainTimes: String(card.remainTimes),
        afterRemainTimes: String(updatedCard.remainTimes),
        description: description || '订单退款/删除导致权益卡禁用',
      });

      disabledCards.push(updatedCard);
    }

    // 检查用户是否还有其他有效的权益卡
    const hasActiveCard = await this.findOne({
      where: {
        customerId,
        status: 'active',
      },
    });

    // 如果没有其他有效的权益卡，将用户会员状态更新为普通会员
    if (!hasActiveCard) {
      await this.customerService.updateMemberStatus(customerId, 0);
    }

    return disabledCards;
  }
}
