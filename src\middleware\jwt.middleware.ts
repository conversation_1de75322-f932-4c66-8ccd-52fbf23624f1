import {
  Middleware,
  IMiddleware,
  Inject,
  Logger,
  ILogger,
} from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/koa';
import { JwtService } from '@midwayjs/jwt';
import { UnauthorizedError } from '@midwayjs/core/dist/error/http';
import { TokenPayload } from '../interface';

/**
 * 对接口返回的数据统一包装
 */
@Middleware()
export class JWTMiddleware implements IMiddleware<Context, NextFunction> {
  @Inject()
  jwtService: JwtService;

  @Logger()
  logger: ILogger;

  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      // 白名单路径直接放行
      if (this.ignore(ctx)) {
        return await next();
      }

      // 获取 token
      const token = this.getTokenFromHeader(ctx);
      if (!token) {
        throw new UnauthorizedError('未登录或登录已过期');
      }

      try {
        // 验证 token
        const decoded = (await this.jwtService.verify(
          token
        )) as unknown as TokenPayload;
        // 将用户信息挂载到 ctx.state 上
        ctx.state.user = decoded;

        // 检查是否需要刷新 token
        if (this.shouldRefreshToken(decoded.exp)) {
          // 确保只复制需要的字段，避免类型错误
          const newToken = this.jwtService.signSync(
            {
              userId: decoded.userId,
              username: decoded.username,
              roles: decoded.roles,
              type: decoded.type,
              userType: decoded.userType,
              // 添加过期时间，使用与原 token 相同的过期时间策略
              iat: Math.floor(Date.now() / 1000),
            },
            { expiresIn: '2h' }
          );

          // 在响应头中返回新token
          ctx.set('New-Token', newToken);
          // 同时设置 Authorization 头，确保当前请求后续处理使用新 token
          ctx.set('Authorization', `Bearer ${newToken}`);

          // 记录 token 刷新日志，便于调试
          this.logger.info(
            `Token 已刷新，原过期时间: ${new Date(
              decoded.exp * 1000
            ).toISOString()}, 新 token 生成时间: ${new Date().toISOString()}`
          );
        }
      } catch (error) {
        this.logger.error('JWT 验证失败:', error);
        throw new UnauthorizedError('未登录或登录已过期');
      }

      await next();
    };
  }

  // 判断是否需要刷新 token
  private shouldRefreshToken(exp: number): boolean {
    const now = Math.floor(Date.now() / 1000);
    const threshold = 30 * 60; // 过期前30分钟刷新
    const timeLeft = exp - now;
    console.log(`Token 剩余有效期: ${timeLeft} 秒, 阈值: ${threshold} 秒`);
    return timeLeft > 0 && timeLeft < threshold;
  }

  ignore(ctx: Context): boolean {
    const whitelist = [
      '/auth/login', // 登录
      '/auth/register', // 注册
      '/public', // 公开接口
      '/weapp', // 微信小程序接口
      '/openapi', // 开放接口
      '/dev-tools', // 开发工具
      '/reviews/service', // 订单描述
      '/order-statistics/debug-time', // 时间调试接口
    ];
    return whitelist.some(path => ctx.path.startsWith(path));
  }

  private getTokenFromHeader(ctx: Context): string | null {
    const authorization = ctx.get('Authorization');
    if (!authorization || !authorization.startsWith('Bearer ')) {
      return null;
    }
    return authorization.substring(7);
  }

  static getName(): string {
    return 'JWT_AUTH';
  }
}
