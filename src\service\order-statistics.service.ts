import { Provide, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Op, fn, col, literal } from 'sequelize';
import {
  Order,
  Customer,
  Employee,
  OrderDetail,
  Service,
  ServiceType,
  AdditionalService,
  AdditionalServiceOrder,
  AdditionalServiceOrderDetail,
} from '../entity';
import { OrderStatus } from '../common/Constant';

@Provide()
export class OrderStatisticsService {
  @Inject()
  ctx: Context;

  /**
   * 获取待办订单总数
   */
  async getPendingOrderCount() {
    const pendingStatuses = [
      OrderStatus.待接单,
      OrderStatus.待服务,
      OrderStatus.退款中,
    ];

    // 统计主订单待办数量
    const mainOrderCount = await Order.count({
      where: {
        status: {
          [Op.in]: pendingStatuses,
        },
      },
    });

    // 统计追加服务订单待办数量
    const additionalServicePendingStatuses = [
      'pending_confirm', // 待确认
      'confirmed', // 已确认（等待付款）
      'refunding', // 退款中
    ];

    const additionalOrderCount = await AdditionalServiceOrder.count({
      where: {
        status: {
          [Op.in]: additionalServicePendingStatuses,
        },
      },
    });

    const totalCount = mainOrderCount + additionalOrderCount;

    return {
      count: totalCount,
      mainOrderCount,
      additionalOrderCount,
      statuses: pendingStatuses,
      additionalStatuses: additionalServicePendingStatuses,
    };
  }

  /**
   * 获取订单概览统计
   */
  async getOrderOverview(startDate?: string, endDate?: string) {
    console.log('=== 订单概览统计开始 ===');
    console.log('输入参数:', { startDate, endDate });

    const whereCondition: any = {};
    const additionalWhereCondition: any = {};

    if (startDate && endDate) {
      console.log('使用指定时间范围:', startDate, '到', endDate);
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
      additionalWhereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    } else {
      console.log('未指定时间范围，使用默认统计');
    }

    // 总订单统计（主订单 + 追加服务订单）
    const totalMainOrders = await Order.count({ where: whereCondition });
    const totalAdditionalOrders = await AdditionalServiceOrder.count({
      where: additionalWhereCondition,
    });
    const totalOrders = totalMainOrders + totalAdditionalOrders;

    console.log('总订单统计:', {
      totalMainOrders,
      totalAdditionalOrders,
      totalOrders
    });

    // 今日订单统计 - 使用本地时间计算，但转换为UTC进行数据库查询
    const now = new Date();
    console.log('当前时间:', now.toISOString(), '本地时间:', now.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }));

    // 获取本地时间的年月日
    const localYear = now.getFullYear();
    const localMonth = now.getMonth();
    const localDate = now.getDate();

    console.log('本地时间组件:', { localYear, localMonth: localMonth + 1, localDate });

    // 创建本地时间的今日开始和结束时间
    const todayLocal = new Date(localYear, localMonth, localDate, 0, 0, 0, 0);
    const tomorrowLocal = new Date(localYear, localMonth, localDate + 1, 0, 0, 0, 0);

    // 转换为UTC时间用于数据库查询（因为数据库配置了+08:00时区，Sequelize会自动处理）
    const today = todayLocal;
    const tomorrow = tomorrowLocal;

    console.log('今日时间范围:', {
      start: today.toISOString(),
      end: tomorrow.toISOString(),
      startLocal: today.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      endLocal: tomorrow.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
    });

    // 如果没有指定时间范围，才统计今日数据
    let todayOrders = 0;
    if (!startDate || !endDate) {
      console.log('开始统计今日订单...');

      const todayMainOrders = await Order.count({
        where: {
          createdAt: {
            [Op.between]: [today, tomorrow],
          },
        },
      });

      const todayAdditionalOrders = await AdditionalServiceOrder.count({
        where: {
          createdAt: {
            [Op.between]: [today, tomorrow],
          },
        },
      });

      todayOrders = todayMainOrders + todayAdditionalOrders;

      console.log('今日订单统计结果:', {
        todayMainOrders,
        todayAdditionalOrders,
        todayOrders
      });
    }

    // 本月订单统计 - 使用本地时间计算
    const monthStartLocal = new Date(localYear, localMonth, 1, 0, 0, 0, 0);
    const monthEndLocal = new Date(localYear, localMonth + 1, 1, 0, 0, 0, 0);

    const monthStart = monthStartLocal;
    const monthEnd = monthEndLocal;

    console.log('本月时间范围:', {
      start: monthStart.toISOString(),
      end: monthEnd.toISOString(),
      startLocal: monthStart.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      endLocal: monthEnd.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
    });

    // 如果没有指定时间范围，才统计本月数据
    let monthOrders = 0;
    if (!startDate || !endDate) {
      console.log('开始统计本月订单...');

      const monthMainOrders = await Order.count({
        where: {
          createdAt: {
            [Op.between]: [monthStart, monthEnd],
          },
        },
      });

      const monthAdditionalOrders = await AdditionalServiceOrder.count({
        where: {
          createdAt: {
            [Op.between]: [monthStart, monthEnd],
          },
        },
      });

      monthOrders = monthMainOrders + monthAdditionalOrders;

      console.log('本月订单统计结果:', {
        monthMainOrders,
        monthAdditionalOrders,
        monthOrders
      });
    }

    // 各状态订单统计（只统计主订单状态）
    const statusStats = await Order.findAll({
      where: whereCondition,
      attributes: ['status', [fn('COUNT', col('id')), 'count']],
      group: ['status'],
      raw: true,
    });

    // 收入统计 - 包含主订单和追加服务订单
    const revenueCondition = {
      ...whereCondition,
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    const additionalRevenueCondition = {
      ...additionalWhereCondition,
      status: {
        [Op.in]: ['completed'], // 追加服务的已完成状态
      },
    };

    // 主订单收入
    const mainRevenueStats = await Order.findAll({
      where: revenueCondition,
      attributes: [
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('COUNT', col('id')), 'completedOrders'],
      ],
      raw: true,
    });

    // 追加服务订单收入
    const additionalRevenueStats = await AdditionalServiceOrder.findAll({
      where: additionalRevenueCondition,
      attributes: [
        [fn('SUM', col('totalFee')), 'totalRevenue'],
        [fn('COUNT', col('id')), 'completedOrders'],
      ],
      raw: true,
    });

    const mainRevenue = parseFloat(
      (mainRevenueStats[0] as any)?.totalRevenue || '0'
    );
    const additionalRevenue = parseFloat(
      (additionalRevenueStats[0] as any)?.totalRevenue || '0'
    );
    const totalRevenue = mainRevenue + additionalRevenue;

    const mainCompletedOrders = parseInt(
      (mainRevenueStats[0] as any)?.completedOrders || '0'
    );
    const additionalCompletedOrders = parseInt(
      (additionalRevenueStats[0] as any)?.completedOrders || '0'
    );
    const totalCompletedOrders =
      mainCompletedOrders + additionalCompletedOrders;

    // 今日收入（如果没有指定时间范围）
    let todayRevenue = 0;
    if (!startDate || !endDate) {
      const todayMainRevenue = await Order.findAll({
        where: {
          createdAt: {
            [Op.between]: [today, tomorrow],
          },
          status: {
            [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
          },
        },
        attributes: [[fn('SUM', col('totalFee')), 'todayRevenue']],
        raw: true,
      });

      const todayAdditionalRevenue = await AdditionalServiceOrder.findAll({
        where: {
          createdAt: {
            [Op.between]: [today, tomorrow],
          },
          status: 'completed',
        },
        attributes: [[fn('SUM', col('totalFee')), 'todayRevenue']],
        raw: true,
      });

      todayRevenue =
        parseFloat((todayMainRevenue[0] as any)?.todayRevenue || '0') +
        parseFloat((todayAdditionalRevenue[0] as any)?.todayRevenue || '0');
    }

    // 本月收入（如果没有指定时间范围）
    let monthRevenue = 0;
    if (!startDate || !endDate) {
      const monthMainRevenue = await Order.findAll({
        where: {
          createdAt: {
            [Op.between]: [monthStart, monthEnd],
          },
          status: {
            [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
          },
        },
        attributes: [[fn('SUM', col('totalFee')), 'monthRevenue']],
        raw: true,
      });

      const monthAdditionalRevenue = await AdditionalServiceOrder.findAll({
        where: {
          createdAt: {
            [Op.between]: [monthStart, monthEnd],
          },
          status: 'completed',
        },
        attributes: [[fn('SUM', col('totalFee')), 'monthRevenue']],
        raw: true,
      });

      monthRevenue =
        parseFloat((monthMainRevenue[0] as any)?.monthRevenue || '0') +
        parseFloat((monthAdditionalRevenue[0] as any)?.monthRevenue || '0');
    }

    const finalResult = {
      orderStats: {
        total: totalOrders,
        mainOrders: totalMainOrders,
        additionalOrders: totalAdditionalOrders,
        today: todayOrders,
        month: monthOrders,
      },
      revenueStats: {
        total: totalRevenue,
        mainRevenue,
        additionalRevenue,
        today: todayRevenue,
        month: monthRevenue,
        completed: totalCompletedOrders,
      },
    };

    console.log('=== 最终统计结果 ===');
    console.log('订单统计:', finalResult.orderStats);
    console.log('收入统计:', finalResult.revenueStats);
    console.log('=== 统计结束 ===');

    return {
      orderStats: {
        total: totalOrders,
        mainOrders: totalMainOrders,
        additionalOrders: totalAdditionalOrders,
        today: todayOrders,
        month: monthOrders,
      },
      statusStats: statusStats.map((item: any) => ({
        status: item.status,
        count: parseInt(item.count),
      })),
      revenueStats: {
        total: parseFloat(totalRevenue.toFixed(2)),
        mainRevenue: parseFloat(mainRevenue.toFixed(2)),
        additionalRevenue: parseFloat(additionalRevenue.toFixed(2)),
        today: parseFloat(todayRevenue.toFixed(2)),
        month: parseFloat(monthRevenue.toFixed(2)),
        completedOrders: totalCompletedOrders,
        mainCompletedOrders,
        additionalCompletedOrders,
      },
    };
  }

  /**
   * 获取订单趋势统计
   */
  async getOrderTrend(
    startDate: string,
    endDate: string,
    groupBy: 'day' | 'week' | 'month' = 'day'
  ) {
    let groupFormat: string;

    switch (groupBy) {
      case 'week':
        groupFormat = 'YEARWEEK(createdAt)';
        break;
      case 'month':
        groupFormat = 'DATE_FORMAT(createdAt, "%Y-%m")';
        break;
      default:
        groupFormat = 'DATE(createdAt)';
    }

    // 主订单趋势数据
    const mainTrendData = await Order.findAll({
      where: {
        createdAt: {
          [Op.between]: [new Date(startDate), new Date(endDate)],
        },
      },
      attributes: [
        [literal(groupFormat), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      group: [literal(groupFormat) as any],
      order: [[literal(groupFormat) as any, 'ASC']],
      raw: true,
    });

    // 追加服务订单趋势数据
    const additionalTrendData = await AdditionalServiceOrder.findAll({
      where: {
        createdAt: {
          [Op.between]: [new Date(startDate), new Date(endDate)],
        },
      },
      attributes: [
        [literal(groupFormat), 'period'],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      group: [literal(groupFormat) as any],
      order: [[literal(groupFormat) as any, 'ASC']],
      raw: true,
    });

    // 合并主订单和追加服务订单数据
    const periodMap = new Map();

    // 处理主订单数据
    mainTrendData.forEach((item: any) => {
      const period = item.period;
      periodMap.set(period, {
        period,
        mainOrderCount: parseInt(item.orderCount),
        mainTotalAmount: parseFloat(item.totalAmount || '0'),
        additionalOrderCount: 0,
        additionalTotalAmount: 0,
      });
    });

    // 处理追加服务订单数据
    additionalTrendData.forEach((item: any) => {
      const period = item.period;
      const existing = periodMap.get(period) || {
        period,
        mainOrderCount: 0,
        mainTotalAmount: 0,
        additionalOrderCount: 0,
        additionalTotalAmount: 0,
      };

      existing.additionalOrderCount = parseInt(item.orderCount);
      existing.additionalTotalAmount = parseFloat(item.totalAmount || '0');
      periodMap.set(period, existing);
    });

    // 转换为最终结果
    const result = Array.from(periodMap.values()).map((item: any) => {
      const totalOrderCount = item.mainOrderCount + item.additionalOrderCount;
      const totalAmount = item.mainTotalAmount + item.additionalTotalAmount;

      return {
        period: item.period,
        orderCount: totalOrderCount,
        mainOrderCount: item.mainOrderCount,
        additionalOrderCount: item.additionalOrderCount,
        totalAmount: parseFloat(totalAmount.toFixed(2)),
        mainTotalAmount: parseFloat(item.mainTotalAmount.toFixed(2)),
        additionalTotalAmount: parseFloat(
          item.additionalTotalAmount.toFixed(2)
        ),
        avgAmount:
          totalOrderCount > 0
            ? parseFloat((totalAmount / totalOrderCount).toFixed(2))
            : 0,
      };
    });

    // 按时间排序
    return result.sort((a, b) => a.period.localeCompare(b.period));
  }

  /**
   * 获取订单状态分布统计
   */
  async getStatusDistribution(startDate?: string, endDate?: string) {
    const whereCondition: any = {};
    const additionalWhereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
      additionalWhereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 主订单状态分布
    const mainStatusData = await Order.findAll({
      where: whereCondition,
      attributes: [
        'status',
        [fn('COUNT', col('id')), 'count'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      group: ['status'],
      raw: true,
    });

    // 追加服务订单状态分布
    const additionalStatusData = await AdditionalServiceOrder.findAll({
      where: additionalWhereCondition,
      attributes: [
        'status',
        [fn('COUNT', col('id')), 'count'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      group: ['status'],
      raw: true,
    });

    // 合并状态数据
    const statusMap = new Map();

    // 处理主订单状态
    mainStatusData.forEach((item: any) => {
      const status = `主订单-${item.status}`;
      statusMap.set(status, {
        status,
        orderType: 'main',
        originalStatus: item.status,
        count: parseInt(item.count),
        totalAmount: parseFloat(item.totalAmount || '0'),
      });
    });

    // 处理追加服务订单状态
    additionalStatusData.forEach((item: any) => {
      const status = `追加服务-${item.status}`;
      statusMap.set(status, {
        status,
        orderType: 'additional',
        originalStatus: item.status,
        count: parseInt(item.count),
        totalAmount: parseFloat(item.totalAmount || '0'),
      });
    });

    const allStatusData = Array.from(statusMap.values());
    const total = allStatusData.reduce((sum, item) => sum + item.count, 0);

    return {
      mainOrderStats: mainStatusData.map((item: any) => ({
        status: item.status,
        count: parseInt(item.count),
        percentage:
          total > 0
            ? ((parseInt(item.count) / total) * 100).toFixed(2)
            : '0.00',
        totalAmount: parseFloat(item.totalAmount || '0'),
      })),
      additionalOrderStats: additionalStatusData.map((item: any) => ({
        status: item.status,
        count: parseInt(item.count),
        percentage:
          total > 0
            ? ((parseInt(item.count) / total) * 100).toFixed(2)
            : '0.00',
        totalAmount: parseFloat(item.totalAmount || '0'),
      })),
      combinedStats: allStatusData.map((item: any) => ({
        status: item.status,
        orderType: item.orderType,
        originalStatus: item.originalStatus,
        count: item.count,
        percentage:
          total > 0 ? ((item.count / total) * 100).toFixed(2) : '0.00',
        totalAmount: parseFloat(item.totalAmount.toFixed(2)),
      })),
      summary: {
        totalOrders: total,
        mainOrderTotal: mainStatusData.reduce(
          (sum, item: any) => sum + parseInt(item.count),
          0
        ),
        additionalOrderTotal: additionalStatusData.reduce(
          (sum, item: any) => sum + parseInt(item.count),
          0
        ),
      },
    };
  }

  /**
   * 获取员工订单统计
   */
  async getEmployeeOrderStatistics(
    startDate?: string,
    endDate?: string,
    employeeId?: number,
    sortBy: 'orderCount' | 'totalAmount' | 'rating' = 'orderCount',
    sortOrder: 'asc' | 'desc' = 'desc',
    page = 1,
    pageSize = 20
  ) {
    const whereCondition: any = {};
    const additionalWhereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
      additionalWhereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    if (employeeId) {
      whereCondition.employeeId = employeeId;
      additionalWhereCondition.employeeId = employeeId;
    }

    // 只统计已完成的订单
    whereCondition.status = {
      [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
    };
    additionalWhereCondition.status = 'completed';

    // 主订单统计
    const mainOrderStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        'employeeId',
        [fn('COUNT', col('Order.id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      include: [
        {
          model: Employee,
          attributes: ['id', 'name', 'phone', 'avatar', 'rating'],
          required: true,
        },
      ],
      group: ['employeeId', 'employee.id'],
      raw: false,
    });

    // 追加服务订单统计
    const additionalOrderStats = await AdditionalServiceOrder.findAll({
      where: additionalWhereCondition,
      attributes: [
        'employeeId',
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      group: ['employeeId'],
      raw: true,
    });

    // 合并统计数据
    const employeeMap = new Map();

    // 处理主订单数据
    mainOrderStats.forEach((item: any) => {
      const employeeId = item.employeeId;
      employeeMap.set(employeeId, {
        employeeId,
        employeeName: item.employee?.name,
        employeePhone: item.employee?.phone,
        employeeAvatar: item.employee?.avatar,
        employeeRating: item.employee?.rating,
        mainOrderCount: parseInt(item.get('orderCount')),
        mainTotalAmount: parseFloat(item.get('totalAmount') || '0'),
        additionalOrderCount: 0,
        additionalTotalAmount: 0,
      });
    });

    // 处理追加服务订单数据
    additionalOrderStats.forEach((item: any) => {
      const employeeId = item.employeeId;
      const existing = employeeMap.get(employeeId);
      if (existing) {
        existing.additionalOrderCount = parseInt(item.orderCount);
        existing.additionalTotalAmount = parseFloat(item.totalAmount || '0');
      } else {
        // 如果员工只有追加服务订单，需要查询员工信息
        employeeMap.set(employeeId, {
          employeeId,
          employeeName: null, // 需要后续查询
          employeePhone: null,
          employeeAvatar: null,
          employeeRating: null,
          mainOrderCount: 0,
          mainTotalAmount: 0,
          additionalOrderCount: parseInt(item.orderCount),
          additionalTotalAmount: parseFloat(item.totalAmount || '0'),
        });
      }
    });

    // 查询缺失的员工信息
    const employeesNeedInfo = Array.from(employeeMap.values()).filter(
      item => item.employeeName === null
    );

    if (employeesNeedInfo.length > 0) {
      const employees = await Employee.findAll({
        where: {
          id: {
            [Op.in]: employeesNeedInfo.map(item => item.employeeId),
          },
        },
        attributes: ['id', 'name', 'phone', 'avatar', 'rating'],
      });

      employees.forEach(emp => {
        const item = employeeMap.get(emp.id);
        if (item) {
          item.employeeName = emp.name;
          item.employeePhone = emp.phone;
          item.employeeAvatar = emp.avatar;
          item.employeeRating = emp.rating;
        }
      });
    }

    // 计算合并后的统计数据
    const combinedStats = Array.from(employeeMap.values()).map(item => {
      const totalOrderCount = item.mainOrderCount + item.additionalOrderCount;
      const totalAmount = item.mainTotalAmount + item.additionalTotalAmount;

      return {
        ...item,
        orderCount: totalOrderCount,
        totalAmount: parseFloat(totalAmount.toFixed(2)),
        avgAmount:
          totalOrderCount > 0
            ? parseFloat((totalAmount / totalOrderCount).toFixed(2))
            : 0,
      };
    });

    // 排序
    let sortField: string;
    switch (sortBy) {
      case 'totalAmount':
        sortField = 'totalAmount';
        break;
      case 'rating':
        sortField = 'employeeRating';
        break;
      default:
        sortField = 'orderCount';
    }

    combinedStats.sort((a, b) => {
      const aVal = a[sortField] || 0;
      const bVal = b[sortField] || 0;
      return sortOrder === 'desc' ? bVal - aVal : aVal - bVal;
    });

    // 分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedStats = combinedStats.slice(startIndex, endIndex);

    return {
      list: paginatedStats,
      total: combinedStats.length,
      page,
      pageSize,
    };
  }

  /**
   * 获取客户订单统计
   */
  async getCustomerOrderStatistics(
    startDate?: string,
    endDate?: string,
    customerId?: number,
    sortBy: 'orderCount' | 'totalAmount' | 'avgAmount' = 'totalAmount',
    sortOrder: 'asc' | 'desc' = 'desc',
    page = 1,
    pageSize = 20
  ) {
    const whereCondition: any = {};
    const additionalWhereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
      additionalWhereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    if (customerId) {
      whereCondition.customerId = customerId;
      additionalWhereCondition.customerId = customerId;
    }

    // 统计所有已支付的订单
    whereCondition.status = {
      [Op.notIn]: [OrderStatus.待付款, OrderStatus.已取消],
    };
    additionalWhereCondition.status = {
      [Op.notIn]: ['pending_confirm', 'rejected', 'cancelled'],
    };

    // 主订单统计
    const mainOrderStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        'customerId',
        [fn('COUNT', col('Order.id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      include: [
        {
          model: Customer,
          attributes: ['id', 'nickname', 'phone', 'avatar', 'memberStatus'],
          required: true,
        },
      ],
      group: ['customerId', 'customer.id'],
      raw: false,
    });

    // 追加服务订单统计
    const additionalOrderStats = await AdditionalServiceOrder.findAll({
      where: additionalWhereCondition,
      attributes: [
        'customerId',
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
      ],
      group: ['customerId'],
      raw: true,
    });

    // 合并统计数据
    const customerMap = new Map();

    // 处理主订单数据
    mainOrderStats.forEach((item: any) => {
      const customerId = item.customerId;
      customerMap.set(customerId, {
        customerId,
        customerName: item.customer?.nickname,
        customerPhone: item.customer?.phone,
        customerAvatar: item.customer?.avatar,
        memberStatus: item.customer?.memberStatus,
        mainOrderCount: parseInt(item.get('orderCount')),
        mainTotalAmount: parseFloat(item.get('totalAmount') || '0'),
        additionalOrderCount: 0,
        additionalTotalAmount: 0,
      });
    });

    // 处理追加服务订单数据
    additionalOrderStats.forEach((item: any) => {
      const customerId = item.customerId;
      const existing = customerMap.get(customerId);
      if (existing) {
        existing.additionalOrderCount = parseInt(item.orderCount);
        existing.additionalTotalAmount = parseFloat(item.totalAmount || '0');
      } else {
        // 如果客户只有追加服务订单，需要查询客户信息
        customerMap.set(customerId, {
          customerId,
          customerName: null, // 需要后续查询
          customerPhone: null,
          customerAvatar: null,
          memberStatus: null,
          mainOrderCount: 0,
          mainTotalAmount: 0,
          additionalOrderCount: parseInt(item.orderCount),
          additionalTotalAmount: parseFloat(item.totalAmount || '0'),
        });
      }
    });

    // 查询缺失的客户信息
    const customersNeedInfo = Array.from(customerMap.values()).filter(
      item => item.customerName === null
    );

    if (customersNeedInfo.length > 0) {
      const customers = await Customer.findAll({
        where: {
          id: {
            [Op.in]: customersNeedInfo.map(item => item.customerId),
          },
        },
        attributes: ['id', 'nickname', 'phone', 'avatar', 'memberStatus'],
      });

      customers.forEach(customer => {
        const item = customerMap.get(customer.id);
        if (item) {
          item.customerName = customer.nickname;
          item.customerPhone = customer.phone;
          item.customerAvatar = customer.avatar;
          item.memberStatus = customer.memberStatus;
        }
      });
    }

    // 计算合并后的统计数据
    const combinedStats = Array.from(customerMap.values()).map(item => {
      const totalOrderCount = item.mainOrderCount + item.additionalOrderCount;
      const totalAmount = item.mainTotalAmount + item.additionalTotalAmount;

      return {
        ...item,
        orderCount: totalOrderCount,
        totalAmount: parseFloat(totalAmount.toFixed(2)),
        avgAmount:
          totalOrderCount > 0
            ? parseFloat((totalAmount / totalOrderCount).toFixed(2))
            : 0,
      };
    });

    // 排序
    let sortField: string;
    switch (sortBy) {
      case 'totalAmount':
        sortField = 'totalAmount';
        break;
      case 'avgAmount':
        sortField = 'avgAmount';
        break;
      default:
        sortField = 'orderCount';
    }

    combinedStats.sort((a, b) => {
      const aVal = a[sortField] || 0;
      const bVal = b[sortField] || 0;
      return sortOrder === 'desc' ? bVal - aVal : aVal - bVal;
    });

    // 分页
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedStats = combinedStats.slice(startIndex, endIndex);

    return {
      list: paginatedStats,
      total: combinedStats.length,
      page,
      pageSize,
    };
  }

  /**
   * 获取服务类型订单统计
   */
  async getServiceTypeStatistics(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    const serviceTypeStats = await OrderDetail.findAll({
      attributes: [
        [fn('COUNT', col('OrderDetail.id')), 'orderCount'],
        [fn('SUM', col('order.totalFee')), 'totalAmount'],
      ],
      include: [
        {
          model: Order,
          where: whereCondition,
          attributes: [],
          required: true,
        },
        {
          model: Service,
          attributes: ['id', 'serviceName'],
          include: [
            {
              model: ServiceType,
              attributes: ['id', 'name', 'type'],
            },
          ],
          required: true,
        },
      ],
      group: [
        'service.serviceType.id',
        'service.id',
        'service.serviceName',
        'service.serviceType.name',
        'service.serviceType.type',
      ],
      order: [[fn('COUNT', col('OrderDetail.id')), 'DESC']],
      raw: false,
    });

    return serviceTypeStats.map((item: any) => ({
      serviceTypeId: item.service?.serviceType?.id,
      serviceTypeName: item.service?.serviceType?.name,
      serviceTypeCode: item.service?.serviceType?.type,
      orderCount: parseInt(item.get('orderCount')),
      totalAmount: parseFloat(item.get('totalAmount') || '0'),
    }));
  }

  /**
   * 获取地区订单统计
   */
  async getRegionStatistics(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 按地址统计订单分布
    const regionStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        'address',
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
        [fn('AVG', col('totalFee')), 'avgAmount'],
      ],
      group: ['address'],
      order: [[fn('COUNT', col('id')), 'DESC']],
      limit: 50, // 限制返回前50个地区
      raw: true,
    });

    return regionStats.map((item: any) => ({
      region: item.address,
      orderCount: parseInt(item.orderCount),
      totalAmount: parseFloat(item.totalAmount || '0'),
      avgAmount: parseFloat(item.avgAmount || '0'),
    }));
  }

  /**
   * 获取时段订单统计
   */
  async getTimePeriodStatistics(
    startDate?: string,
    endDate?: string,
    periodType: 'hour' | 'weekday' = 'hour'
  ) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    let groupBy: string;
    let selectField: string;

    if (periodType === 'hour') {
      groupBy = 'HOUR(createdAt)';
      selectField = 'hour';
    } else {
      groupBy = 'WEEKDAY(createdAt)';
      selectField = 'weekday';
    }

    const timePeriodStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        [literal(groupBy), selectField],
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('totalFee')), 'totalAmount'],
        [fn('AVG', col('totalFee')), 'avgAmount'],
      ],
      group: [literal(groupBy) as any],
      order: [[literal(groupBy) as any, 'ASC']],
      raw: true,
    });

    return timePeriodStats.map((item: any) => ({
      period: item[selectField],
      periodLabel:
        periodType === 'hour'
          ? `${item[selectField]}:00-${item[selectField] + 1}:00`
          : ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][
              item[selectField]
            ],
      orderCount: parseInt(item.orderCount),
      totalAmount: parseFloat(item.totalAmount || '0'),
      avgAmount: parseFloat(item.avgAmount || '0'),
    }));
  }

  /**
   * 获取订单金额分布统计
   */
  async getAmountDistribution(startDate?: string, endDate?: string) {
    const whereCondition: any = {};

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 定义金额区间
    const amountRanges = [
      { min: 0, max: 50, label: '0-50元' },
      { min: 50, max: 100, label: '50-100元' },
      { min: 100, max: 200, label: '100-200元' },
      { min: 200, max: 500, label: '200-500元' },
      { min: 500, max: 1000, label: '500-1000元' },
      { min: 1000, max: 999999, label: '1000元以上' },
    ];

    const distributionStats = await Promise.all(
      amountRanges.map(async range => {
        const count = await Order.count({
          where: {
            ...whereCondition,
            totalFee: {
              [Op.gte]: range.min,
              [Op.lt]: range.max,
            },
          },
        });

        return {
          range: range.label,
          min: range.min,
          max: range.max,
          count,
        };
      })
    );

    const total = distributionStats.reduce((sum, item) => sum + item.count, 0);

    return distributionStats.map(item => ({
      ...item,
      percentage: total > 0 ? ((item.count / total) * 100).toFixed(2) : '0.00',
    }));
  }

  /**
   * 获取用户有效订单原价统计
   * 只统计已完成和已评价的订单
   */
  async getCustomerOriginalPriceStatistics(
    customerId: number,
    startDate?: string,
    endDate?: string
  ) {
    const whereCondition: any = {
      customerId,
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 主订单统计
    const orderStats = await Order.findAll({
      where: whereCondition,
      attributes: [
        [fn('COUNT', col('id')), 'orderCount'],
        [fn('SUM', col('originalPrice')), 'totalOriginalPrice'],
        [fn('SUM', col('totalFee')), 'totalPaidAmount'],
        [fn('SUM', col('cardDeduction')), 'totalCardDeduction'],
        [fn('SUM', col('couponDeduction')), 'totalCouponDeduction'],
        [
          fn('SUM', col('additionalServiceOriginalPrice')),
          'totalAdditionalOriginalPrice',
        ],
        [
          fn('SUM', col('additionalServiceAmount')),
          'totalAdditionalPaidAmount',
        ],
      ],
      raw: true,
    });

    const stats = orderStats[0] as any;

    return {
      customerId,
      orderCount: parseInt(stats.orderCount || '0'),
      // 主订单原价统计
      mainOrderOriginalPrice: parseFloat(stats.totalOriginalPrice || '0'),
      mainOrderPaidAmount: parseFloat(stats.totalPaidAmount || '0'),
      // 追加服务原价统计
      additionalServiceOriginalPrice: parseFloat(
        stats.totalAdditionalOriginalPrice || '0'
      ),
      additionalServicePaidAmount: parseFloat(
        stats.totalAdditionalPaidAmount || '0'
      ),
      // 总计
      totalOriginalPrice:
        parseFloat(stats.totalOriginalPrice || '0') +
        parseFloat(stats.totalAdditionalOriginalPrice || '0'),
      totalPaidAmount:
        parseFloat(stats.totalPaidAmount || '0') +
        parseFloat(stats.totalAdditionalPaidAmount || '0'),
      // 优惠统计
      totalCardDeduction: parseFloat(stats.totalCardDeduction || '0'),
      totalCouponDeduction: parseFloat(stats.totalCouponDeduction || '0'),
      totalDeduction:
        parseFloat(stats.totalCardDeduction || '0') +
        parseFloat(stats.totalCouponDeduction || '0'),
    };
  }

  /**
   * 获取用户有效订单列表
   * 只查询已完成和已评价的订单，包含详细的服务和增项服务信息
   */
  async getCustomerValidOrderList(
    customerId: number,
    startDate?: string,
    endDate?: string,
    sortBy:
      | 'orderTime'
      | 'serviceTime'
      | 'originalPrice'
      | 'totalFee' = 'orderTime',
    sortOrder: 'asc' | 'desc' = 'desc',
    page = 1,
    pageSize = 20
  ) {
    const whereCondition: any = {
      customerId,
      status: {
        [Op.in]: [OrderStatus.已完成, OrderStatus.已评价],
      },
    };

    if (startDate && endDate) {
      whereCondition.createdAt = {
        [Op.between]: [new Date(startDate), new Date(endDate)],
      };
    }

    // 构建排序条件
    let orderBy: any;
    switch (sortBy) {
      case 'serviceTime':
        orderBy = ['serviceTime', sortOrder.toUpperCase()];
        break;
      case 'originalPrice':
        orderBy = ['originalPrice', sortOrder.toUpperCase()];
        break;
      case 'totalFee':
        orderBy = ['totalFee', sortOrder.toUpperCase()];
        break;
      default:
        orderBy = ['orderTime', sortOrder.toUpperCase()];
    }

    const orders = await Order.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: OrderDetail,
          include: [
            {
              model: Service,
              include: [
                {
                  model: ServiceType,
                  attributes: ['id', 'name', 'type'],
                },
              ],
              attributes: ['id', 'serviceName', 'basePrice', 'serviceTypeId'],
            },
            {
              model: AdditionalService,
              attributes: ['id', 'name', 'type', 'price'],
              through: { attributes: [] },
            },
            {
              model: AdditionalServiceOrder,
              include: [
                {
                  model: AdditionalServiceOrderDetail,
                  as: 'details',
                  include: [
                    {
                      model: AdditionalService,
                      attributes: ['id', 'name', 'price', 'type'],
                    },
                  ],
                  attributes: [
                    'id',
                    'serviceId',
                    'serviceName',
                    'servicePrice',
                    'quantity',
                  ],
                },
              ],
              attributes: [
                'id',
                'sn',
                'status',
                'originalPrice',
                'totalFee',
                'cardDeduction',
                'couponDeduction',
                'createdAt',
              ],
            },
          ],
          attributes: [
            'id',
            'serviceId',
            'serviceName',
            'servicePrice',
            'petName',
            'petType',
            'petBreed',
            'userRemark',
          ],
        },
      ],
      order: [orderBy],
      offset: (page - 1) * pageSize,
      limit: pageSize,
      attributes: [
        'id',
        'sn',
        'status',
        'orderTime',
        'serviceTime',
        'originalPrice',
        'totalFee',
        'cardDeduction',
        'couponDeduction',
        'additionalServiceOriginalPrice',
        'additionalServiceAmount',
        'address',
        'addressDetail',
      ],
    });

    return {
      list: orders.rows.map((order: any) => {
        // 计算主订单服务信息
        const mainServices = (order.orderDetails || []).map((detail: any) => ({
          id: detail.id,
          serviceId: detail.serviceId,
          serviceName: detail.serviceName,
          servicePrice: parseFloat(detail.servicePrice || '0'),
          serviceType: detail.service?.serviceType?.name || '',
          serviceTypeName: detail.service?.serviceType?.type || '',
          petName: detail.petName,
          petType: detail.petType,
          petBreed: detail.petBreed,
          userRemark: detail.userRemark,
          // 订单详情关联的增项服务
          additionalServices: (detail.additionalServices || []).map(
            (addService: any) => ({
              id: addService.id,
              name: addService.name,
              type: addService.type,
              price: parseFloat(addService.price || '0'),
            })
          ),
        }));

        // 计算追加服务信息
        const additionalServiceOrders = (order.orderDetails || []).flatMap(
          (detail: any) =>
            (detail.additionalServiceOrders || []).map((addOrder: any) => ({
              id: addOrder.id,
              sn: addOrder.sn,
              status: addOrder.status,
              originalPrice: parseFloat(addOrder.originalPrice || '0'),
              totalFee: parseFloat(addOrder.totalFee || '0'),
              cardDeduction: parseFloat(addOrder.cardDeduction || '0'),
              couponDeduction: parseFloat(addOrder.couponDeduction || '0'),
              createdAt: addOrder.createdAt,
              services: (addOrder.details || []).map((orderDetail: any) => ({
                id: orderDetail.id,
                serviceId: orderDetail.serviceId,
                serviceName: orderDetail.serviceName,
                servicePrice: parseFloat(orderDetail.servicePrice || '0'),
                quantity: orderDetail.quantity,
                serviceType: orderDetail.service?.serviceType?.name || '',
                serviceTypeName: orderDetail.service?.serviceType?.type || '',
              })),
            }))
        );

        return {
          id: order.id,
          sn: order.sn,
          status: order.status,
          orderTime: order.orderTime,
          serviceTime: order.serviceTime,
          address: order.address,
          addressDetail: order.addressDetail,
          // 价格信息
          originalPrice: parseFloat(order.originalPrice || '0'),
          totalFee: parseFloat(order.totalFee || '0'),
          cardDeduction: parseFloat(order.cardDeduction || '0'),
          couponDeduction: parseFloat(order.couponDeduction || '0'),
          additionalServiceOriginalPrice: parseFloat(
            order.additionalServiceOriginalPrice || '0'
          ),
          additionalServiceAmount: parseFloat(
            order.additionalServiceAmount || '0'
          ),
          // 总计价格
          totalOriginalPrice:
            parseFloat(order.originalPrice || '0') +
            parseFloat(order.additionalServiceOriginalPrice || '0'),
          totalPaidAmount:
            parseFloat(order.totalFee || '0') +
            parseFloat(order.additionalServiceAmount || '0'),
          // 服务信息
          mainServices,
          additionalServiceOrders,
        };
      }),
      total: orders.count,
      page,
      pageSize,
    };
  }

  /**
   * 调试时间条件
   */
  async debugTimeConditions() {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
    const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0, 0);
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);
    const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1, 0, 0, 0, 0);

    // 查询今日订单
    const todayMainOrders = await Order.count({
      where: {
        createdAt: {
          [Op.between]: [today, tomorrow],
        },
      },
    });

    const todayAdditionalOrders = await AdditionalServiceOrder.count({
      where: {
        createdAt: {
          [Op.between]: [today, tomorrow],
        },
      },
    });

    // 查询本月订单
    const monthMainOrders = await Order.count({
      where: {
        createdAt: {
          [Op.between]: [monthStart, monthEnd],
        },
      },
    });

    const monthAdditionalOrders = await AdditionalServiceOrder.count({
      where: {
        createdAt: {
          [Op.between]: [monthStart, monthEnd],
        },
      },
    });

    // 查询最近的订单
    const recentOrders = await Order.findAll({
      attributes: ['id', 'sn', 'status', 'createdAt', 'totalFee'],
      order: [['createdAt', 'DESC']],
      limit: 5,
    });

    const recentAdditionalOrders = await AdditionalServiceOrder.findAll({
      attributes: ['id', 'status', 'createdAt', 'totalFee'],
      order: [['createdAt', 'DESC']],
      limit: 5,
    });

    // 查询8月1日的订单
    const aug1Start = new Date(2025, 7, 1, 0, 0, 0, 0); // 8月1日开始
    const aug2Start = new Date(2025, 7, 2, 0, 0, 0, 0); // 8月2日开始

    const aug1MainOrders = await Order.count({
      where: {
        createdAt: {
          [Op.between]: [aug1Start, aug2Start],
        },
      },
    });

    const aug1AdditionalOrders = await AdditionalServiceOrder.count({
      where: {
        createdAt: {
          [Op.between]: [aug1Start, aug2Start],
        },
      },
    });

    return {
      currentTime: {
        now: now.toISOString(),
        localTime: now.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
      },
      timeRanges: {
        today: {
          start: today.toISOString(),
          end: tomorrow.toISOString(),
        },
        month: {
          start: monthStart.toISOString(),
          end: monthEnd.toISOString(),
        },
        aug1: {
          start: aug1Start.toISOString(),
          end: aug2Start.toISOString(),
        },
      },
      counts: {
        today: {
          mainOrders: todayMainOrders,
          additionalOrders: todayAdditionalOrders,
          total: todayMainOrders + todayAdditionalOrders,
        },
        month: {
          mainOrders: monthMainOrders,
          additionalOrders: monthAdditionalOrders,
          total: monthMainOrders + monthAdditionalOrders,
        },
        aug1: {
          mainOrders: aug1MainOrders,
          additionalOrders: aug1AdditionalOrders,
          total: aug1MainOrders + aug1AdditionalOrders,
        },
      },
      recentData: {
        mainOrders: recentOrders.map(order => ({
          id: order.id,
          sn: order.sn,
          status: order.status,
          createdAt: order.createdAt,
          localTime: order.createdAt ? new Date(order.createdAt).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }) : null,
          totalFee: order.totalFee,
        })),
        additionalOrders: recentAdditionalOrders.map(order => ({
          id: order.id,
          status: order.status,
          createdAt: order.createdAt,
          localTime: order.createdAt ? new Date(order.createdAt).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }) : null,
          totalFee: order.totalFee,
        })),
      },
    };
  }
}
