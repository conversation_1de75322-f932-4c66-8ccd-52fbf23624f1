# 订单统计接口

## 概述
订单统计接口提供全面的订单数据分析功能，包括订单概览、趋势分析、状态分布、员工维度统计、客户维度统计等。

## 接口列表

### 1. 获取待办订单总数

**接口地址：** `GET /order-statistics/pending-count`

**接口描述：** 查询所有待办订单的总数，包括待接单、待服务、退款中三种状态的订单

**请求参数：** 无

**请求示例：**
```
GET /order-statistics/pending-count
```

**响应数据：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "count": 18,
    "mainOrderCount": 15,
    "additionalOrderCount": 3,
    "statuses": ["待接单", "待服务", "退款中"],
    "additionalStatuses": ["pending_confirm", "confirmed", "refunding"]
  }
}
```

**响应字段说明：**
| 字段名 | 类型 | 描述 |
|--------|------|------|
| count | number | 待办订单总数（主订单+追加服务订单） |
| mainOrderCount | number | 主订单待办数量 |
| additionalOrderCount | number | 追加服务订单待办数量 |
| statuses | string[] | 主订单包含的状态列表 |
| additionalStatuses | string[] | 追加服务订单包含的状态列表 |

**业务说明：**
- 主订单待办状态包括：
  - `待接单`：已付款但尚未被员工接单的订单
  - `待服务`：已接单但尚未开始服务的订单
  - `退款中`：正在处理退款的订单
- 追加服务订单待办状态包括：
  - `pending_confirm`：待确认的追加服务
  - `confirmed`：已确认等待付款的追加服务
  - `refunding`：退款中的追加服务
- 该接口主要用于管理端首页显示待办事项数量

---

### 2. 获取订单概览统计

**接口地址：** `GET /order-statistics/overview`

**接口描述：** 获取订单概览统计数据，包括总订单数、今日订单、本月订单、各状态分布、收入统计等

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**请求示例：**
```
GET /order-statistics/overview
GET /order-statistics/overview?startDate=2024-01-01&endDate=2024-12-31
```

**响应数据：**
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "orderStats": {
      "total": 1350,
      "mainOrders": 1250,
      "additionalOrders": 100,
      "today": 18,
      "month": 350
    },
    "statusStats": [
      {
        "status": "已完成",
        "count": 800
      },
      {
        "status": "待接单",
        "count": 25
      }
    ],
    "revenueStats": {
      "total": 135000.00,
      "mainRevenue": 125000.00,
      "additionalRevenue": 10000.00,
      "today": 2800.00,
      "month": 28000.00,
      "completedOrders": 850,
      "mainCompletedOrders": 800,
      "additionalCompletedOrders": 50
    }
  }
}
```

**响应字段说明：**

**orderStats（订单统计）：**
| 字段名 | 类型 | 描述 |
|--------|------|------|
| total | number | 总订单数（主订单+追加服务订单） |
| mainOrders | number | 主订单数量 |
| additionalOrders | number | 追加服务订单数量 |
| today | number | 今日订单数（仅在未指定时间范围时返回） |
| month | number | 本月订单数（仅在未指定时间范围时返回） |

**revenueStats（收入统计）：**
| 字段名 | 类型 | 描述 |
|--------|------|------|
| total | number | 总收入（主订单+追加服务订单） |
| mainRevenue | number | 主订单收入 |
| additionalRevenue | number | 追加服务订单收入 |
| today | number | 今日收入（仅在未指定时间范围时返回） |
| month | number | 本月收入（仅在未指定时间范围时返回） |
| completedOrders | number | 已完成订单总数 |
| mainCompletedOrders | number | 主订单已完成数量 |
| additionalCompletedOrders | number | 追加服务已完成数量 |

---

### 3. 获取订单趋势统计

**接口地址：** `GET /order-statistics/trend`

**接口描述：** 获取指定时间范围内的订单趋势数据

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 是 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 是 | 结束日期，格式：YYYY-MM-DD |
| groupBy | string | 否 | 分组方式：day/week/month，默认day |

**请求示例：**
```
GET /order-statistics/trend?startDate=2024-01-01&endDate=2024-01-31&groupBy=day
```

---

### 4. 获取订单状态分布统计

**接口地址：** `GET /order-statistics/status-distribution`

**接口描述：** 获取订单状态分布统计数据

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**请求示例：**
```
GET /order-statistics/status-distribution
```

---

### 5. 获取员工订单统计

**接口地址：** `GET /order-statistics/employee`

**接口描述：** 获取员工维度的订单统计数据

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| employeeId | number | 否 | 员工ID，指定员工时只返回该员工数据 |
| sortBy | string | 否 | 排序字段：orderCount/totalAmount/rating，默认orderCount |
| sortOrder | string | 否 | 排序方式：asc/desc，默认desc |
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**请求示例：**
```
GET /order-statistics/employee?current=1&pageSize=10&sortBy=totalAmount&sortOrder=desc
```

---

### 6. 获取客户订单统计

**接口地址：** `GET /order-statistics/customer`

**接口描述：** 获取客户维度的订单统计数据

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| customerId | number | 否 | 客户ID，指定客户时只返回该客户数据 |
| sortBy | string | 否 | 排序字段：totalAmount/orderCount，默认totalAmount |
| sortOrder | string | 否 | 排序方式：asc/desc，默认desc |
| current | number | 否 | 当前页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |

**请求示例：**
```
GET /order-statistics/customer?current=1&pageSize=10
```

---

### 7. 获取服务类型订单统计

**接口地址：** `GET /order-statistics/service-type`

**接口描述：** 获取服务类型维度的订单统计数据

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**请求示例：**
```
GET /order-statistics/service-type
```

---

### 8. 获取地区订单统计

**接口地址：** `GET /order-statistics/region`

**接口描述：** 获取地区维度的订单统计数据

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**请求示例：**
```
GET /order-statistics/region
```

---

### 9. 获取时段订单统计

**接口地址：** `GET /order-statistics/time-period`

**接口描述：** 获取时段维度的订单统计数据

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |
| periodType | string | 否 | 时段类型：hour/day，默认hour |

**请求示例：**
```
GET /order-statistics/time-period?periodType=hour
```

---

### 10. 获取订单金额分布统计

**接口地址：** `GET /order-statistics/amount-distribution`

**接口描述：** 获取订单金额分布统计数据

**请求参数：**
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| startDate | string | 否 | 开始日期，格式：YYYY-MM-DD |
| endDate | string | 否 | 结束日期，格式：YYYY-MM-DD |

**请求示例：**
```
GET /order-statistics/amount-distribution
```

## 注意事项

1. **时间范围**：所有支持时间范围的接口，如果不传时间参数，默认统计全部数据
2. **分页**：支持分页的接口默认返回前20条数据，最大支持100条
3. **排序**：支持多种排序方式，默认按相关性排序
4. **权限**：所有统计接口都需要管理员权限
5. **缓存**：部分统计数据可能有缓存，实时性要求高的场景请注意
