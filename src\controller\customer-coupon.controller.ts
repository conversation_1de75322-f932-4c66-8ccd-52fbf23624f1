import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomerCouponService } from '../service/customer-coupon.service';
import { CustomError } from '../error/custom.error';
import { Coupon, User } from '../entity';

@Controller('/customer-coupons')
export class CustomerCouponController {
  @Inject()
  ctx: Context;

  @Inject()
  service: CustomerCouponService;

  @Get('/', { summary: '查询用户代金券列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: Coupon,
        },
        {
          model: User,
        },
      ],
    });
  }

  @Get('/:id', { summary: '按ID查询用户代金券' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定用户代金券');
    }
    return res;
  }

  @Post('/', { summary: '新增用户代金券' })
  async create(@Body() info: any) {
    // 检查是否是管理员发放（通过operatorId字段判断）
    const { operatorId, ...couponData } = info;
    const isAdminGrant = !!operatorId;

    // 使用 createCoupon 方法，确保领取时间有默认值
    const res = await this.service.createCoupon(
      couponData,
      isAdminGrant,
      operatorId
    );
    return res;
  }

  @Put('/:id', { summary: '更新用户代金券' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除用户代金券' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/:couponId/users', { summary: '根据代金券ID获取用户列表' })
  async getUsersByCardType(
    @Param('couponId') couponId: number,
    @Query() query: any
  ) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    const res = await this.service.getUsersByCoupon({
      query: { ...queryInfo },
      offset,
      limit,
      couponId,
    });
    return res;
  }

  @Get('/:customerId/coupons', {
    summary: '根据用户ID获取代金券列表',
  })
  async getCardsByUser(
    @Param('customerId') customerId: number,
    @Query() query: any
  ) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    const res = await this.service.getCardsByUser({
      ...queryInfo,
      offset,
      limit,
      customerId,
    });
    return res;
  }
}
