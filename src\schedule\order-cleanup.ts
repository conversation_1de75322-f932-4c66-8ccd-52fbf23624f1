import { Provide, Inject, Logger } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { Job, IJob } from '@midwayjs/cron';
import { OrderService } from '../service/order.service';
import { Order } from '../entity/order.entity';
import { OrderStatus } from '../common/Constant';
import { Op } from 'sequelize';

@Provide()
@Job('orderCleanupJob', {
  cronTime: '0 0 2 * * *', // 每天凌晨2点执行
  start: true, // 应用启动时自动启动任务
})
export class OrderCleanupJob implements IJob {
  @Inject()
  orderService: OrderService;

  @Logger()
  logger: ILogger;

  async onTick() {
    this.logger.info('开始执行订单清理定时任务...');

    try {
      // 删除超时未付款订单
      await this.deleteExpiredUnpaidOrders();

      // 删除超时已取消订单
      await this.deleteExpiredCancelledOrders();

      this.logger.info('订单清理定时任务执行完成');
    } catch (error) {
      this.logger.error('订单清理定时任务执行失败:', error);
    }
  }

  /**
   * 删除超时未付款订单
   * 删除所有状态为"待付款"且创建时间超过30分钟的订单
   */
  private async deleteExpiredUnpaidOrders() {
    this.logger.info('开始删除超时未付款订单...');

    try {
      // 计算30分钟前的时间
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);

      // 查找超时未付款的订单
      const expiredOrders = await Order.findAll({
        where: {
          status: OrderStatus.待付款,
          createdAt: {
            [Op.lt]: thirtyMinutesAgo,
          },
        },
        attributes: ['id', 'sn', 'createdAt'],
      });

      if (expiredOrders.length === 0) {
        this.logger.info('没有找到超时未付款的订单');
        return;
      }

      this.logger.info(
        `找到 ${expiredOrders.length} 个超时未付款订单，开始删除...`
      );

      let deletedCount = 0;
      let failedCount = 0;

      // 逐个删除订单
      for (const order of expiredOrders) {
        try {
          // 先取消订单，再删除（使用管理员权限）
          await this.orderService.adminCancelOrder(
            order.id,
            0, // 系统操作员ID设为0
            '系统自动删除超时未付款订单'
          );
          await this.orderService.deleteOrder(order.id);

          deletedCount++;
          this.logger.info(`成功删除超时订单: ${order.sn} (ID: ${order.id})`);
        } catch (error) {
          failedCount++;
          this.logger.error(
            `删除超时订单失败: ${order.sn} (ID: ${order.id})`,
            error
          );
        }
      }

      this.logger.info(
        `超时未付款订单删除完成，成功删除: ${deletedCount} 个，失败: ${failedCount} 个`
      );
    } catch (error) {
      this.logger.error('删除超时未付款订单失败:', error);
    }
  }

  /**
   * 删除超时已取消订单
   * 删除所有状态为"已取消"且创建时间超过1周的订单
   */
  private async deleteExpiredCancelledOrders() {
    this.logger.info('开始删除超时已取消订单...');

    try {
      // 计算1周前的时间
      const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

      // 查找超时已取消的订单
      const expiredOrders = await Order.findAll({
        where: {
          status: OrderStatus.已取消,
          createdAt: {
            [Op.lt]: oneWeekAgo,
          },
        },
        attributes: ['id', 'sn', 'createdAt'],
      });

      if (expiredOrders.length === 0) {
        this.logger.info('没有找到超时已取消的订单');
        return;
      }

      this.logger.info(
        `找到 ${expiredOrders.length} 个超时已取消订单，开始删除...`
      );

      let deletedCount = 0;
      let failedCount = 0;

      // 逐个删除订单
      for (const order of expiredOrders) {
        try {
          // 直接删除已取消的订单
          await this.orderService.deleteOrder(order.id);

          deletedCount++;
          this.logger.info(`成功删除已取消订单: ${order.sn} (ID: ${order.id})`);
        } catch (error) {
          failedCount++;
          this.logger.error(
            `删除已取消订单失败: ${order.sn} (ID: ${order.id})`,
            error
          );
        }
      }

      this.logger.info(
        `超时已取消订单删除完成，成功删除: ${deletedCount} 个，失败: ${failedCount} 个`
      );
    } catch (error) {
      this.logger.error('删除超时已取消订单失败:', error);
    }
  }
}
