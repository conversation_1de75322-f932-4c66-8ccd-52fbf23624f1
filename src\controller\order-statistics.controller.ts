import { Controller, Get, Query, Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { OrderStatisticsService } from '../service/order-statistics.service';
import {
  OrderStatisticsQueryDTO,
  EmployeeOrderStatisticsQueryDTO,
  CustomerOrderStatisticsQueryDTO,
  TimePeriodStatisticsQueryDTO,
  CustomerOriginalPriceStatisticsQueryDTO,
  CustomerValidOrderListQueryDTO,
} from '../dto/order-statistics.dto';

@Controller('/order-statistics')
export class OrderStatisticsController {
  @Inject()
  ctx: Context;

  @Inject()
  orderStatisticsService: OrderStatisticsService;

  /**
   * 获取待办订单总数
   */
  @Get('/pending-count', { summary: '查询待办订单总数' })
  async getPendingCount() {
    return await this.orderStatisticsService.getPendingOrderCount();
  }

  /**
   * 获取订单概览统计
   */
  @Get('/overview')
  async getOverview(@Query() query: OrderStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    return await this.orderStatisticsService.getOrderOverview(
      startDate,
      endDate
    );
  }

  /**
   * 获取订单趋势统计
   */
  @Get('/trend')
  async getTrend(@Query() query: OrderStatisticsQueryDTO) {
    const { startDate, endDate, groupBy = 'day' } = query;

    if (!startDate || !endDate) {
      return {
        code: 400,
        message: '开始日期和结束日期不能为空',
      };
    }

    return await this.orderStatisticsService.getOrderTrend(
      startDate,
      endDate,
      groupBy
    );
  }

  /**
   * 获取订单状态分布统计
   */
  @Get('/status-distribution')
  async getStatusDistribution(@Query() query: OrderStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    return await this.orderStatisticsService.getStatusDistribution(
      startDate,
      endDate
    );
  }

  /**
   * 获取员工订单统计
   */
  @Get('/employee')
  async getEmployeeStatistics(@Query() query: EmployeeOrderStatisticsQueryDTO) {
    const {
      startDate,
      endDate,
      employeeId,
      sortBy = 'orderCount',
      sortOrder = 'desc',
      pageSize = 20,
      current = 1,
    } = query;

    const actualPage = current;
    const actualPageSize = pageSize;

    return await this.orderStatisticsService.getEmployeeOrderStatistics(
      startDate,
      endDate,
      employeeId,
      sortBy,
      sortOrder,
      actualPage,
      actualPageSize
    );
  }

  /**
   * 获取客户订单统计
   */
  @Get('/customer')
  async getCustomerStatistics(@Query() query: CustomerOrderStatisticsQueryDTO) {
    const {
      startDate,
      endDate,
      customerId,
      sortBy = 'totalAmount',
      sortOrder = 'desc',
      current = 1,
      pageSize = 20,
    } = query;

    return await this.orderStatisticsService.getCustomerOrderStatistics(
      startDate,
      endDate,
      customerId,
      sortBy,
      sortOrder,
      current,
      pageSize
    );
  }

  /**
   * 获取服务类型订单统计
   */
  @Get('/service-type')
  async getServiceTypeStatistics(@Query() query: OrderStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    return await this.orderStatisticsService.getServiceTypeStatistics(
      startDate,
      endDate
    );
  }

  /**
   * 获取地区订单统计
   */
  @Get('/region')
  async getRegionStatistics(@Query() query: OrderStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    return await this.orderStatisticsService.getRegionStatistics(
      startDate,
      endDate
    );
  }

  /**
   * 获取时段订单统计
   */
  @Get('/time-period')
  async getTimePeriodStatistics(@Query() query: TimePeriodStatisticsQueryDTO) {
    const { startDate, endDate, periodType = 'hour' } = query;
    return await this.orderStatisticsService.getTimePeriodStatistics(
      startDate,
      endDate,
      periodType
    );
  }

  /**
   * 获取订单金额分布统计
   */
  @Get('/amount-distribution')
  async getAmountDistribution(@Query() query: OrderStatisticsQueryDTO) {
    const { startDate, endDate } = query;
    return await this.orderStatisticsService.getAmountDistribution(
      startDate,
      endDate
    );
  }

  /**
   * 获取用户有效订单原价统计
   */
  @Get('/customer-original-price')
  async getCustomerOriginalPriceStatistics(
    @Query() query: CustomerOriginalPriceStatisticsQueryDTO
  ) {
    const { customerId, startDate, endDate } = query;
    return await this.orderStatisticsService.getCustomerOriginalPriceStatistics(
      customerId,
      startDate,
      endDate
    );
  }

  /**
   * 获取用户有效订单列表
   */
  @Get('/customer-valid-orders')
  async getCustomerValidOrderList(
    @Query() query: CustomerValidOrderListQueryDTO
  ) {
    const {
      customerId,
      startDate,
      endDate,
      sortBy = 'orderTime',
      sortOrder = 'desc',
      current = 1,
      pageSize = 20,
    } = query;

    return await this.orderStatisticsService.getCustomerValidOrderList(
      customerId,
      startDate,
      endDate,
      sortBy,
      sortOrder,
      current,
      pageSize
    );
  }

  @Get('/debug-time', { summary: '调试时间条件' })
  async debugTime() {
    return await this.orderStatisticsService.debugTimeConditions();
  }

  @Get('/debug-overview', { summary: '调试订单概览' })
  async debugOverview(@Query() query: OrderStatisticsQueryDTO) {
    console.log('=== 调试订单概览接口调用 ===');
    console.log('接收到的查询参数:', query);
    const result = await this.orderStatisticsService.getOrderOverview(query.startDate, query.endDate);
    console.log('返回结果:', JSON.stringify(result, null, 2));
    return result;
  }
}
